'use client';

import { useState } from 'react';
import { useRequireAuth } from '@/hooks/useAuth';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/shared/Card';
import { Button } from '@/components/shared/Button';
import { Input } from '@/components/shared/Input';
import { Badge } from '@/components/shared/Badge';
import { 
  Plus, 
  Minus, 
  Trash2, 
  ShoppingCart, 
  CreditCard,
  Banknote,
  Smartphone,
  Search
} from 'lucide-react';
import { formatCurrency, generateOrderNumber } from '@/lib/utils';
import { CartItem, Menu } from '@/types';

// Mock menu data
const mockMenus: Menu[] = [
  {
    id: '1',
    name: 'Pizza Margherita',
    description: 'Pizza klasik dengan tomat, mozzarella, dan basil',
    price: 85000,
    category: 'pizza',
    image_url: null,
    is_available: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Pizza Pepperoni',
    description: 'Pizza dengan pepperoni dan keju mozzarella',
    price: 95000,
    category: 'pizza',
    image_url: null,
    is_available: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '3',
    name: 'Coca Cola',
    description: 'Minuman soda segar',
    price: 15000,
    category: 'beverage',
    image_url: null,
    is_available: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '4',
    name: 'Garlic Bread',
    description: 'Roti bawang putih yang renyah',
    price: 25000,
    category: 'appetizer',
    image_url: null,
    is_available: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

const categories = [
  { id: 'all', name: 'Semua', count: mockMenus.length },
  { id: 'pizza', name: 'Pizza', count: mockMenus.filter(m => m.category === 'pizza').length },
  { id: 'beverage', name: 'Minuman', count: mockMenus.filter(m => m.category === 'beverage').length },
  { id: 'appetizer', name: 'Appetizer', count: mockMenus.filter(m => m.category === 'appetizer').length },
];

export default function CashierPage() {
  const { profile } = useRequireAuth(['admin', 'cashier']);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'digital_wallet'>('cash');

  if (!profile) return null;

  // Filter menus based on category and search
  const filteredMenus = mockMenus.filter(menu => {
    const matchesCategory = selectedCategory === 'all' || menu.category === selectedCategory;
    const matchesSearch = menu.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         menu.description?.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch && menu.is_available;
  });

  // Add item to cart
  const addToCart = (menu: Menu) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.menu.id === menu.id);
      if (existingItem) {
        return prevCart.map(item =>
          item.menu.id === menu.id
            ? { ...item, quantity: item.quantity + 1, subtotal: (item.quantity + 1) * menu.price }
            : item
        );
      } else {
        return [...prevCart, { menu, quantity: 1, subtotal: menu.price }];
      }
    });
  };

  // Update cart item quantity
  const updateCartQuantity = (menuId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(menuId);
      return;
    }
    
    setCart(prevCart =>
      prevCart.map(item =>
        item.menu.id === menuId
          ? { ...item, quantity: newQuantity, subtotal: newQuantity * item.menu.price }
          : item
      )
    );
  };

  // Remove item from cart
  const removeFromCart = (menuId: string) => {
    setCart(prevCart => prevCart.filter(item => item.menu.id !== menuId));
  };

  // Calculate total
  const total = cart.reduce((sum, item) => sum + item.subtotal, 0);

  // Process order
  const processOrder = () => {
    if (cart.length === 0) return;

    const orderNumber = generateOrderNumber();
    
    // Here you would normally send the order to your API
    console.log('Processing order:', {
      order_number: orderNumber,
      customer_name: customerName || null,
      customer_phone: customerPhone || null,
      payment_method: paymentMethod,
      total_amount: total,
      items: cart,
      cashier_id: profile.id,
    });

    // Reset form
    setCart([]);
    setCustomerName('');
    setCustomerPhone('');
    setPaymentMethod('cash');

    alert(`Pesanan ${orderNumber} berhasil dibuat!`);
  };

  return (
    <DashboardLayout>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-8rem)]">
        {/* Menu Section */}
        <div className="lg:col-span-2 space-y-4">
          {/* Search and Categories */}
          <div className="space-y-4">
            <Input
              placeholder="Cari menu..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
            
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.name} ({category.count})
                </Button>
              ))}
            </div>
          </div>

          {/* Menu Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 overflow-y-auto max-h-[calc(100vh-16rem)]">
            {filteredMenus.map(menu => (
              <Card key={menu.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-sm">{menu.name}</h3>
                      <Badge variant="outline" className="text-xs">
                        {menu.category}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {menu.description}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className="font-bold text-primary">
                        {formatCurrency(menu.price)}
                      </span>
                      <Button
                        size="sm"
                        onClick={() => addToCart(menu)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Cart Section */}
        <div className="space-y-4">
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Keranjang ({cart.length})
              </CardTitle>
            </CardHeader>
            
            <CardContent className="flex-1 flex flex-col space-y-4">
              {/* Customer Info */}
              <div className="space-y-2">
                <Input
                  placeholder="Nama pelanggan (opsional)"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                />
                <Input
                  placeholder="No. telepon (opsional)"
                  value={customerPhone}
                  onChange={(e) => setCustomerPhone(e.target.value)}
                />
              </div>

              {/* Cart Items */}
              <div className="flex-1 overflow-y-auto space-y-2">
                {cart.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <ShoppingCart className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>Keranjang kosong</p>
                  </div>
                ) : (
                  cart.map(item => (
                    <div key={item.menu.id} className="border rounded-lg p-3 space-y-2">
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium text-sm">{item.menu.name}</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFromCart(item.menu.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateCartQuantity(item.menu.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center text-sm">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateCartQuantity(item.menu.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                        <span className="font-medium text-sm">
                          {formatCurrency(item.subtotal)}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Payment Method */}
              {cart.length > 0 && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Metode Pembayaran</label>
                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant={paymentMethod === 'cash' ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setPaymentMethod('cash')}
                    >
                      <Banknote className="h-4 w-4 mr-1" />
                      Tunai
                    </Button>
                    <Button
                      variant={paymentMethod === 'card' ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setPaymentMethod('card')}
                    >
                      <CreditCard className="h-4 w-4 mr-1" />
                      Kartu
                    </Button>
                    <Button
                      variant={paymentMethod === 'digital_wallet' ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setPaymentMethod('digital_wallet')}
                    >
                      <Smartphone className="h-4 w-4 mr-1" />
                      Digital
                    </Button>
                  </div>
                </div>
              )}

              {/* Total and Checkout */}
              {cart.length > 0 && (
                <div className="space-y-4 pt-4 border-t">
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Total:</span>
                    <span className="text-primary">{formatCurrency(total)}</span>
                  </div>
                  
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={processOrder}
                  >
                    Proses Pesanan
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
