import { supabase, getUserProfile } from '@/lib/supabase';
import { Profile, LoginForm, ApiResponse } from '@/types';
import { AuthError, User } from '@supabase/supabase-js';

export class AuthService {
  /**
   * Sign in with email and password
   */
  static async signIn(credentials: LoginForm): Promise<ApiResponse<{ user: User; profile: Profile }>> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      if (!data.user) {
        return {
          data: null,
          error: 'No user data returned',
          success: false,
        };
      }

      // Get user profile
      const profile = await getUserProfile(data.user.id);
      if (!profile) {
        return {
          data: null,
          error: 'User profile not found',
          success: false,
        };
      }

      return {
        data: {
          user: data.user,
          profile,
        },
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Sign out current user
   */
  static async signOut(): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get current user session
   */
  static async getCurrentSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error getting session:', error);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  /**
   * Get current user
   */
  static async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error) {
        console.error('Error getting user:', error);
        return null;
      }

      return user;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }

  /**
   * Get current user with profile
   */
  static async getCurrentUserWithProfile(): Promise<{ user: User; profile: Profile } | null> {
    try {
      const user = await this.getCurrentUser();
      if (!user) return null;

      const profile = await getUserProfile(user.id);
      if (!profile) return null;

      return { user, profile };
    } catch (error) {
      console.error('Error getting user with profile:', error);
      return null;
    }
  }

  /**
   * Check if user has required role
   */
  static async hasRole(requiredRoles: string[]): Promise<boolean> {
    try {
      const userWithProfile = await this.getCurrentUserWithProfile();
      if (!userWithProfile) return false;

      return requiredRoles.includes(userWithProfile.profile.role);
    } catch (error) {
      console.error('Error checking user role:', error);
      return false;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(updates: Partial<Profile>): Promise<ApiResponse<Profile>> {
    try {
      const user = await this.getCurrentUser();
      if (!user) {
        return {
          data: null,
          error: 'No authenticated user',
          success: false,
        };
      }

      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Change password
   */
  static async changePassword(newPassword: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Reset password
   */
  static async resetPassword(email: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Listen to auth state changes
   */
  static onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}
