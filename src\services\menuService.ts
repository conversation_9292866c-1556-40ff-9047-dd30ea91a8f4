import { supabase } from '@/lib/supabase';
import { Menu, MenuInsert, MenuUpdate, ApiResponse } from '@/types';

export class MenuService {
  /**
   * Get all menus with optional filters
   */
  static async getMenus(filters?: {
    category?: string;
    is_available?: boolean;
    search?: string;
  }): Promise<ApiResponse<Menu[]>> {
    try {
      let query = supabase
        .from('menus')
        .select('*')
        .order('category')
        .order('name');

      // Apply filters
      if (filters?.category) {
        query = query.eq('category', filters.category);
      }
      
      if (filters?.is_available !== undefined) {
        query = query.eq('is_available', filters.is_available);
      }
      
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get available menus only
   */
  static async getAvailableMenus(filters?: {
    category?: string;
    search?: string;
  }): Promise<ApiResponse<Menu[]>> {
    return this.getMenus({
      ...filters,
      is_available: true,
    });
  }

  /**
   * Get menu by ID
   */
  static async getMenuById(id: string): Promise<ApiResponse<Menu>> {
    try {
      const { data, error } = await supabase
        .from('menus')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Create new menu item
   */
  static async createMenu(menuData: MenuInsert): Promise<ApiResponse<Menu>> {
    try {
      const { data, error } = await supabase
        .from('menus')
        .insert(menuData)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Update menu item
   */
  static async updateMenu(
    id: string,
    updates: MenuUpdate
  ): Promise<ApiResponse<Menu>> {
    try {
      const { data, error } = await supabase
        .from('menus')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Delete menu item
   */
  static async deleteMenu(id: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase
        .from('menus')
        .delete()
        .eq('id', id);

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: null,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Toggle menu availability
   */
  static async toggleAvailability(
    id: string,
    isAvailable: boolean
  ): Promise<ApiResponse<Menu>> {
    try {
      const { data, error } = await supabase
        .from('menus')
        .update({
          is_available: isAvailable,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get menus by category
   */
  static async getMenusByCategory(category: string): Promise<ApiResponse<Menu[]>> {
    return this.getMenus({ category, is_available: true });
  }

  /**
   * Search menus
   */
  static async searchMenus(query: string): Promise<ApiResponse<Menu[]>> {
    return this.getMenus({ search: query, is_available: true });
  }

  /**
   * Get menu categories with counts
   */
  static async getMenuCategories(): Promise<ApiResponse<{
    category: string;
    count: number;
    available_count: number;
  }[]>> {
    try {
      const { data, error } = await supabase
        .from('menus')
        .select('category, is_available');

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      // Group by category and count
      const categoryMap = new Map();
      
      data?.forEach(menu => {
        const category = menu.category;
        if (!categoryMap.has(category)) {
          categoryMap.set(category, { count: 0, available_count: 0 });
        }
        
        const stats = categoryMap.get(category);
        stats.count++;
        if (menu.is_available) {
          stats.available_count++;
        }
      });

      const categories = Array.from(categoryMap.entries()).map(([category, stats]) => ({
        category,
        count: stats.count,
        available_count: stats.available_count,
      }));

      return {
        data: categories,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get popular menus (based on order frequency)
   */
  static async getPopularMenus(limit: number = 10): Promise<ApiResponse<(Menu & { order_count: number })[]>> {
    try {
      const { data, error } = await supabase
        .from('menus')
        .select(`
          *,
          order_items!inner (
            quantity
          )
        `)
        .eq('is_available', true);

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      // Calculate order counts and sort
      const menusWithCounts = data?.map(menu => {
        const orderCount = menu.order_items?.reduce(
          (sum: number, item: any) => sum + item.quantity,
          0
        ) || 0;
        
        return {
          ...menu,
          order_count: orderCount,
        };
      }).sort((a, b) => b.order_count - a.order_count).slice(0, limit);

      return {
        data: menusWithCounts || [],
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }
}
