// Database Types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          role: 'admin' | 'cashier' | 'kitchen' | 'manager';
          avatar_url: string | null;
          phone: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          role?: 'admin' | 'cashier' | 'kitchen' | 'manager';
          avatar_url?: string | null;
          phone?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          role?: 'admin' | 'cashier' | 'kitchen' | 'manager';
          avatar_url?: string | null;
          phone?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      menus: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          price: number;
          category: 'pizza' | 'beverage' | 'appetizer' | 'dessert';
          image_url: string | null;
          is_available: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          price: number;
          category: 'pizza' | 'beverage' | 'appetizer' | 'dessert';
          image_url?: string | null;
          is_available?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          price?: number;
          category?: 'pizza' | 'beverage' | 'appetizer' | 'dessert';
          image_url?: string | null;
          is_available?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          order_number: string;
          customer_name: string | null;
          customer_phone: string | null;
          status: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
          total_amount: number;
          payment_method: 'cash' | 'card' | 'digital_wallet';
          payment_status: 'pending' | 'paid' | 'refunded';
          notes: string | null;
          cashier_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          order_number: string;
          customer_name?: string | null;
          customer_phone?: string | null;
          status?: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
          total_amount: number;
          payment_method: 'cash' | 'card' | 'digital_wallet';
          payment_status?: 'pending' | 'paid' | 'refunded';
          notes?: string | null;
          cashier_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          order_number?: string;
          customer_name?: string | null;
          customer_phone?: string | null;
          status?: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
          total_amount?: number;
          payment_method?: 'cash' | 'card' | 'digital_wallet';
          payment_status?: 'pending' | 'paid' | 'refunded';
          notes?: string | null;
          cashier_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      order_items: {
        Row: {
          id: string;
          order_id: string;
          menu_id: string;
          quantity: number;
          unit_price: number;
          subtotal: number;
          notes: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          menu_id: string;
          quantity: number;
          unit_price: number;
          subtotal: number;
          notes?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          menu_id?: string;
          quantity?: number;
          unit_price?: number;
          subtotal?: number;
          notes?: string | null;
          created_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          order_id: string;
          amount: number;
          payment_method: 'cash' | 'card' | 'digital_wallet';
          status: 'pending' | 'completed' | 'failed' | 'refunded';
          reference_number: string | null;
          processed_by: string;
          processed_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          amount: number;
          payment_method: 'cash' | 'card' | 'digital_wallet';
          status?: 'pending' | 'completed' | 'failed' | 'refunded';
          reference_number?: string | null;
          processed_by: string;
          processed_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          amount?: number;
          payment_method?: 'cash' | 'card' | 'digital_wallet';
          status?: 'pending' | 'completed' | 'failed' | 'refunded';
          reference_number?: string | null;
          processed_by?: string;
          processed_at?: string;
          created_at?: string;
        };
      };
    };
  };
}

// Application Types
export type UserRole = 'admin' | 'cashier' | 'kitchen' | 'manager';
export type OrderStatus = 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
export type PaymentMethod = 'cash' | 'card' | 'digital_wallet';
export type PaymentStatus = 'pending' | 'paid' | 'refunded';
export type MenuCategory = 'pizza' | 'beverage' | 'appetizer' | 'dessert';

// Extracted types for easier use
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Menu = Database['public']['Tables']['menus']['Row'];
export type Order = Database['public']['Tables']['orders']['Row'];
export type OrderItem = Database['public']['Tables']['order_items']['Row'];
export type Transaction = Database['public']['Tables']['transactions']['Row'];

// Insert types
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type MenuInsert = Database['public']['Tables']['menus']['Insert'];
export type OrderInsert = Database['public']['Tables']['orders']['Insert'];
export type OrderItemInsert = Database['public']['Tables']['order_items']['Insert'];
export type TransactionInsert = Database['public']['Tables']['transactions']['Insert'];

// Update types
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];
export type MenuUpdate = Database['public']['Tables']['menus']['Update'];
export type OrderUpdate = Database['public']['Tables']['orders']['Update'];
export type OrderItemUpdate = Database['public']['Tables']['order_items']['Update'];
export type TransactionUpdate = Database['public']['Tables']['transactions']['Update'];

// Extended types for application use
export interface OrderWithItems extends Order {
  order_items: (OrderItem & {
    menu: Menu;
  })[];
  cashier: Profile;
}

export interface CartItem {
  menu: Menu;
  quantity: number;
  notes?: string;
  subtotal: number;
}

export interface DashboardStats {
  todayOrders: number;
  todayRevenue: number;
  pendingOrders: number;
  completedOrders: number;
}

// API Response types
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface CreateOrderForm {
  customer_name?: string;
  customer_phone?: string;
  payment_method: PaymentMethod;
  notes?: string;
  items: CartItem[];
}

// Realtime types
export interface RealtimePayload<T> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  schema: string;
  table: string;
}
