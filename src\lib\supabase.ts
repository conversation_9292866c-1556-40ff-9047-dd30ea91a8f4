import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Create Supabase client for client-side operations
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Create Supabase client for server-side operations (with service role key)
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Helper function to get current user
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    console.error('Error getting current user:', error);
    return null;
  }
  return user;
};

// Helper function to get user profile
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error getting user profile:', error);
    return null;
  }

  return data;
};

// Helper function to check if user has required role
export const checkUserRole = async (userId: string, requiredRoles: string[]) => {
  const profile = await getUserProfile(userId);
  if (!profile) return false;
  
  return requiredRoles.includes(profile.role);
};

// Realtime subscription helper
export const subscribeToTable = <T>(
  table: string,
  callback: (payload: any) => void,
  filter?: string
) => {
  const channel = supabase
    .channel(`${table}_changes`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: table,
        filter: filter,
      },
      callback
    )
    .subscribe();

  return channel;
};

// Helper function to unsubscribe from realtime
export const unsubscribeFromChannel = (channel: any) => {
  supabase.removeChannel(channel);
};

// Database helper functions
export const dbHelpers = {
  // Generic CRUD operations
  async create<T>(table: string, data: any): Promise<T | null> {
    const { data: result, error } = await supabase
      .from(table)
      .insert(data)
      .select()
      .single();

    if (error) {
      console.error(`Error creating ${table}:`, error);
      return null;
    }

    return result;
  },

  async read<T>(
    table: string,
    select: string = '*',
    filters?: Record<string, any>
  ): Promise<T[] | null> {
    let query = supabase.from(table).select(select);

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error reading ${table}:`, error);
      return null;
    }

    return data;
  },

  async update<T>(
    table: string,
    id: string,
    updates: any
  ): Promise<T | null> {
    const { data, error } = await supabase
      .from(table)
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating ${table}:`, error);
      return null;
    }

    return data;
  },

  async delete(table: string, id: string): Promise<boolean> {
    const { error } = await supabase
      .from(table)
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting from ${table}:`, error);
      return false;
    }

    return true;
  },

  // Specific helper for getting single record
  async getById<T>(table: string, id: string, select: string = '*'): Promise<T | null> {
    const { data, error } = await supabase
      .from(table)
      .select(select)
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error getting ${table} by id:`, error);
      return null;
    }

    return data;
  },
};

// Export types for convenience
export type { Database } from '@/types';
