import { QueryClient } from '@tanstack/react-query';

// Create a client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Time in milliseconds that unused/inactive cache data remains in memory
      gcTime: 1000 * 60 * 60 * 24, // 24 hours
      
      // Time in milliseconds after data is considered stale
      staleTime: 1000 * 60 * 5, // 5 minutes
      
      // Retry failed requests
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      
      // Retry delay
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Refetch on window focus
      refetchOnWindowFocus: false,
      
      // Refetch on reconnect
      refetchOnReconnect: true,
      
      // Refetch on mount
      refetchOnMount: true,
    },
    mutations: {
      // Retry failed mutations
      retry: 1,
      
      // Retry delay for mutations
      retryDelay: 1000,
    },
  },
});

// Query keys factory for consistent key management
export const queryKeys = {
  // Auth related
  auth: {
    user: ['auth', 'user'] as const,
    profile: (userId: string) => ['auth', 'profile', userId] as const,
  },
  
  // Menu related
  menus: {
    all: ['menus'] as const,
    list: (filters?: Record<string, any>) => ['menus', 'list', filters] as const,
    detail: (id: string) => ['menus', 'detail', id] as const,
    byCategory: (category: string) => ['menus', 'category', category] as const,
  },
  
  // Order related
  orders: {
    all: ['orders'] as const,
    list: (filters?: Record<string, any>) => ['orders', 'list', filters] as const,
    detail: (id: string) => ['orders', 'detail', id] as const,
    byStatus: (status: string) => ['orders', 'status', status] as const,
    byCashier: (cashierId: string) => ['orders', 'cashier', cashierId] as const,
    today: ['orders', 'today'] as const,
  },
  
  // Transaction related
  transactions: {
    all: ['transactions'] as const,
    list: (filters?: Record<string, any>) => ['transactions', 'list', filters] as const,
    detail: (id: string) => ['transactions', 'detail', id] as const,
    byOrder: (orderId: string) => ['transactions', 'order', orderId] as const,
  },
  
  // Dashboard related
  dashboard: {
    stats: ['dashboard', 'stats'] as const,
    todayOrders: ['dashboard', 'today-orders'] as const,
    revenue: (period: string) => ['dashboard', 'revenue', period] as const,
  },
  
  // Kitchen related
  kitchen: {
    queue: ['kitchen', 'queue'] as const,
    preparing: ['kitchen', 'preparing'] as const,
  },
} as const;

// Helper function to invalidate related queries
export const invalidateQueries = {
  // Invalidate all auth related queries
  auth: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.auth.user });
  },
  
  // Invalidate all menu related queries
  menus: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.menus.all });
  },
  
  // Invalidate all order related queries
  orders: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.orders.all });
  },
  
  // Invalidate all transaction related queries
  transactions: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.transactions.all });
  },
  
  // Invalidate dashboard stats
  dashboard: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.stats });
  },
  
  // Invalidate kitchen queue
  kitchen: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.kitchen.queue });
  },
};

// Helper function to prefetch data
export const prefetchQueries = {
  // Prefetch menus
  menus: async () => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.menus.all,
      queryFn: async () => {
        // This will be implemented in the service layer
        return [];
      },
    });
  },
  
  // Prefetch today's orders
  todayOrders: async () => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.orders.today,
      queryFn: async () => {
        // This will be implemented in the service layer
        return [];
      },
    });
  },
};

// Error handling helper
export const handleQueryError = (error: any) => {
  console.error('Query error:', error);
  
  // You can add global error handling here
  // For example, show toast notifications, redirect to login, etc.
  
  if (error?.status === 401) {
    // Handle unauthorized access
    console.log('Unauthorized access - redirecting to login');
  }
  
  if (error?.status >= 500) {
    // Handle server errors
    console.log('Server error - please try again later');
  }
};

// Optimistic update helpers
export const optimisticUpdates = {
  // Update order status optimistically
  updateOrderStatus: (orderId: string, newStatus: string) => {
    queryClient.setQueryData(
      queryKeys.orders.detail(orderId),
      (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          status: newStatus,
          updated_at: new Date().toISOString(),
        };
      }
    );
  },
  
  // Add new order optimistically
  addOrder: (newOrder: any) => {
    queryClient.setQueryData(
      queryKeys.orders.today,
      (oldData: any[]) => {
        if (!oldData) return [newOrder];
        return [newOrder, ...oldData];
      }
    );
  },
  
  // Update menu availability optimistically
  updateMenuAvailability: (menuId: string, isAvailable: boolean) => {
    queryClient.setQueryData(
      queryKeys.menus.detail(menuId),
      (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          is_available: isAvailable,
          updated_at: new Date().toISOString(),
        };
      }
    );
  },
};
