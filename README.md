# 🍕 LuPizza - Sistem Kasir Restoran

Sistem kasir modern untuk restoran pizza yang dibangun dengan Next.js, Supabase, dan React Query. Dirancang khusus untuk memenuhi kebutuhan operasional restoran dengan fitur real-time dan interface yang user-friendly.

## ✨ Fitur Utama

### 🔐 Autentikasi & Otorisasi

- Login dengan email/password menggunakan Supabase Auth
- Role-based access control (Admin, Manager, Kasir, Dapur)
- Session management otomatis
- Protected routes berdasarkan role

### 💰 Sistem Kasir

- Interface kasir yang intuitif dan responsif
- Manajemen keranjang belanja real-time
- Multiple payment methods (Tunai, Kartu, Digital Wallet)
- Generate nomor pesanan otomatis
- Print receipt (coming soon)

### 👨‍🍳 Dapur & Kitchen Display

- Real-time order notifications
- Kitchen queue management
- Update status pesanan (Pending → Preparing → Ready → Completed)
- Order tracking dengan timestamp

### 📊 Dashboard & Analytics

- Dashboard overview dengan statistik real-time
- Laporan penjualan harian
- Monitoring pesanan aktif
- Performance metrics

### 🍕 Manajemen Menu

- CRUD operations untuk menu items
- Kategorisasi menu (Pizza, Minuman, Appetizer, Dessert)
- Toggle ketersediaan menu
- Upload gambar menu (coming soon)

### 👥 Manajemen Staff

- User management dengan role assignment
- Profile management
- Activity tracking

## 🛠️ Tech Stack

### Frontend

- **Next.js 15** - React framework dengan App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS framework
- **React Query (TanStack Query)** - Data fetching & caching
- **React Hook Form** - Form management
- **Zod** - Schema validation
- **Lucide React** - Icon library

### Backend & Database

- **Supabase** - Backend-as-a-Service
  - PostgreSQL database
  - Real-time subscriptions
  - Row Level Security (RLS)
  - Authentication & authorization
- **Supabase Edge Functions** - Serverless functions (coming soon)

### Development Tools

- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Static type checking

## 📁 Struktur Project

```
lupizza-app/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── auth/
│   │   │   └── login/         # Halaman login
│   │   ├── dashboard/         # Dashboard utama
│   │   │   ├── cashier/       # Interface kasir
│   │   │   ├── kitchen/       # Interface dapur
│   │   │   ├── admin/         # Panel admin
│   │   │   └── page.tsx       # Dashboard overview
│   │   ├── globals.css        # Global styles
│   │   └── layout.tsx         # Root layout
│   ├── components/            # Reusable components
│   │   ├── shared/           # Shared UI components
│   │   ├── cashier/          # Cashier-specific components
│   │   └── providers/        # Context providers
│   ├── hooks/                # Custom React hooks
│   │   ├── useAuth.ts        # Authentication hook
│   │   └── useOrders.ts      # Orders management hook
│   ├── lib/                  # Utility libraries
│   │   ├── supabase.ts       # Supabase client config
│   │   ├── queryClient.ts    # React Query config
│   │   └── utils.ts          # Helper functions
│   ├── services/             # API service layers
│   │   ├── authService.ts    # Authentication services
│   │   ├── orderService.ts   # Order management
│   │   └── menuService.ts    # Menu management
│   ├── types/                # TypeScript type definitions
│   │   └── index.ts          # Database & app types
│   └── styles/               # Additional styles
├── public/                   # Static assets
├── supabase-schema.sql       # Database schema
├── .env.example             # Environment variables template
├── tailwind.config.ts       # Tailwind configuration
├── tsconfig.json           # TypeScript configuration
└── package.json            # Dependencies & scripts
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase account

### 1. Clone Repository

```bash
git clone <repository-url>
cd lupizza-app
```

### 2. Install Dependencies

```bash
npm install
# atau
yarn install
# atau
pnpm install
```

### 3. Setup Environment Variables

```bash
cp .env.example .env.local
```

Edit `.env.local` dengan konfigurasi Supabase Anda:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 4. Setup Database

1. Buka Supabase Dashboard
2. Buat project baru atau gunakan yang sudah ada
3. Jalankan script SQL dari file `supabase-schema.sql` di SQL Editor
4. Pastikan RLS (Row Level Security) sudah aktif

### 5. Run Development Server

```bash
npm run dev
# atau
yarn dev
# atau
pnpm dev
```

Buka [http://localhost:3000](http://localhost:3000) di browser.

## 👤 Akun Demo

Gunakan akun demo berikut untuk testing:

| Role  | Email               | Password   |
| ----- | ------------------- | ---------- |
| Admin | <EMAIL>   | admin123   |
| Kasir | <EMAIL>   | kasir123   |
| Dapur | <EMAIL> | kitchen123 |

## 📖 Panduan Penggunaan

### Untuk Kasir

1. Login dengan akun kasir
2. Pilih menu dari katalog
3. Tambahkan ke keranjang
4. Isi informasi pelanggan (opsional)
5. Pilih metode pembayaran
6. Proses pesanan

### Untuk Dapur

1. Login dengan akun kitchen
2. Monitor antrian pesanan di dashboard dapur
3. Update status pesanan sesuai progress
4. Notifikasi real-time untuk pesanan baru

### Untuk Admin/Manager

1. Login dengan akun admin
2. Akses semua fitur sistem
3. Kelola menu, staff, dan pengaturan
4. Monitor analytics dan laporan

## 🔧 Konfigurasi Lanjutan

### Supabase Configuration

File konfigurasi utama ada di `src/lib/supabase.ts`. Anda dapat menyesuaikan:

- Connection settings
- Realtime configuration
- Auth settings

### React Query Configuration

Konfigurasi caching dan data fetching di `src/lib/queryClient.ts`:

- Cache time settings
- Retry policies
- Background refetch settings

### Tailwind Customization

Sesuaikan tema dan styling di `tailwind.config.ts`:

- Brand colors
- Custom components
- Responsive breakpoints

## 🧪 Testing

```bash
# Run tests (coming soon)
npm run test

# Run E2E tests (coming soon)
npm run test:e2e
```

## 📦 Build & Deployment

### Build untuk Production

```bash
npm run build
npm run start
```

### Deploy ke Vercel

1. Push code ke GitHub
2. Connect repository di Vercel
3. Set environment variables
4. Deploy

### Deploy ke Platform Lain

Project ini compatible dengan platform hosting modern seperti:

- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork repository
2. Buat feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push ke branch (`git push origin feature/amazing-feature`)
5. Buat Pull Request

## 📝 License

Distributed under the MIT License. See `LICENSE` for more information.

## 🆘 Support

Jika mengalami masalah atau butuh bantuan:

1. Check dokumentasi ini
2. Buka issue di GitHub
3. Contact developer team

## 🔮 Roadmap

### Phase 1 (Current)

- ✅ Basic authentication & authorization
- ✅ Cashier interface
- ✅ Kitchen display system
- ✅ Order management
- ✅ Menu management

### Phase 2 (Coming Soon)

- 🔄 Print receipt functionality
- 🔄 Inventory management
- 🔄 Advanced reporting & analytics
- 🔄 Mobile app (React Native)
- 🔄 WhatsApp integration

### Phase 3 (Future)

- 📱 Customer mobile ordering
- 🚚 Delivery management
- 💳 Advanced payment integration
- 📊 Business intelligence dashboard
- 🔔 Advanced notification system

---

**Dibuat dengan ❤️ untuk LuPizza Restaurant**
