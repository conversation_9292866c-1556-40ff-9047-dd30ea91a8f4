'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Mail, Lock, Pizza } from 'lucide-react';
import { useRedirectIfAuthenticated } from '@/hooks/useAuth';
import { Button } from '@/components/shared/Button';
import { Input } from '@/components/shared/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/shared/Card';
import { LoadingPage } from '@/components/shared/Loading';

// Validation schema
const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email wajib diisi')
    .email('Format email tidak valid'),
  password: z
    .string()
    .min(1, 'Password wajib diisi')
    .min(6, 'Password minimal 6 karakter'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const { signIn, signInMutation, isLoading } = useRedirectIfAuthenticated();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      await signIn(data);
    } catch (error: any) {
      // Handle specific error cases
      if (error.message?.includes('Invalid login credentials')) {
        setError('email', { message: 'Email atau password salah' });
        setError('password', { message: 'Email atau password salah' });
      } else if (error.message?.includes('Email not confirmed')) {
        setError('email', { message: 'Email belum diverifikasi' });
      } else {
        setError('email', { message: 'Terjadi kesalahan, silakan coba lagi' });
      }
    }
  };

  // Show loading if checking auth state
  if (isLoading) {
    return <LoadingPage text="Memeriksa status login..." />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-secondary/10 p-4">
      <div className="w-full max-w-md">
        {/* Logo and Brand */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-full mb-4">
            <Pizza className="w-8 h-8 text-primary-foreground" />
          </div>
          <h1 className="text-3xl font-bold text-foreground">LuPizza</h1>
          <p className="text-muted-foreground mt-2">Sistem Kasir Restoran</p>
        </div>

        {/* Login Form */}
        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Masuk</CardTitle>
            <CardDescription className="text-center">
              Masukkan email dan password untuk mengakses sistem
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Email Field */}
              <Input
                {...register('email')}
                type="email"
                label="Email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="h-4 w-4" />}
                error={errors.email?.message}
                disabled={signInMutation.isPending}
              />

              {/* Password Field */}
              <Input
                {...register('password')}
                type={showPassword ? 'text' : 'password'}
                label="Password"
                placeholder="Masukkan password"
                leftIcon={<Lock className="h-4 w-4" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="hover:text-foreground transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                }
                error={errors.password?.message}
                disabled={signInMutation.isPending}
              />

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full"
                isLoading={signInMutation.isPending}
                disabled={signInMutation.isPending}
              >
                {signInMutation.isPending ? 'Memproses...' : 'Masuk'}
              </Button>

              {/* Error Message */}
              {signInMutation.error && (
                <div className="text-sm text-destructive text-center p-3 bg-destructive/10 rounded-md">
                  {signInMutation.error.message || 'Terjadi kesalahan saat login'}
                </div>
              )}
            </form>

            {/* Demo Accounts */}
            <div className="mt-6 pt-6 border-t">
              <p className="text-sm text-muted-foreground text-center mb-3">
                Akun Demo:
              </p>
              <div className="space-y-2 text-xs text-muted-foreground">
                <div className="flex justify-between">
                  <span>Admin:</span>
                  <span><EMAIL> / admin123</span>
                </div>
                <div className="flex justify-between">
                  <span>Kasir:</span>
                  <span><EMAIL> / kasir123</span>
                </div>
                <div className="flex justify-between">
                  <span>Dapur:</span>
                  <span><EMAIL> / kitchen123</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-muted-foreground">
          <p>&copy; 2024 LuPizza. Semua hak dilindungi.</p>
        </div>
      </div>
    </div>
  );
}
