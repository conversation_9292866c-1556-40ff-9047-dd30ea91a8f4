import { supabase } from '@/lib/supabase';
import { 
  Order, 
  OrderWithItems, 
  OrderInsert, 
  OrderUpdate, 
  OrderItem, 
  OrderItemInsert,
  CreateOrderForm,
  ApiResponse 
} from '@/types';
import { generateOrderNumber } from '@/lib/utils';

export class OrderService {
  /**
   * Get all orders with optional filters
   */
  static async getOrders(filters?: {
    status?: string;
    cashier_id?: string;
    date_from?: string;
    date_to?: string;
    limit?: number;
  }): Promise<ApiResponse<OrderWithItems[]>> {
    try {
      let query = supabase
        .from('orders')
        .select(`
          *,
          order_items (
            *,
            menu:menus (*)
          ),
          cashier:profiles!orders_cashier_id_fkey (*)
        `)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      
      if (filters?.cashier_id) {
        query = query.eq('cashier_id', filters.cashier_id);
      }
      
      if (filters?.date_from) {
        query = query.gte('created_at', filters.date_from);
      }
      
      if (filters?.date_to) {
        query = query.lte('created_at', filters.date_to);
      }
      
      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get order by ID
   */
  static async getOrderById(id: string): Promise<ApiResponse<OrderWithItems>> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (
            *,
            menu:menus (*)
          ),
          cashier:profiles!orders_cashier_id_fkey (*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Create new order
   */
  static async createOrder(
    orderData: CreateOrderForm,
    cashierId: string
  ): Promise<ApiResponse<OrderWithItems>> {
    try {
      // Start transaction
      const orderNumber = generateOrderNumber();
      
      // Calculate total
      const totalAmount = orderData.items.reduce(
        (sum, item) => sum + item.subtotal,
        0
      );

      // Create order
      const orderInsert: OrderInsert = {
        order_number: orderNumber,
        customer_name: orderData.customer_name || null,
        customer_phone: orderData.customer_phone || null,
        total_amount: totalAmount,
        payment_method: orderData.payment_method,
        notes: orderData.notes || null,
        cashier_id: cashierId,
      };

      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert(orderInsert)
        .select()
        .single();

      if (orderError) {
        return {
          data: null,
          error: orderError.message,
          success: false,
        };
      }

      // Create order items
      const orderItems: OrderItemInsert[] = orderData.items.map(item => ({
        order_id: order.id,
        menu_id: item.menu.id,
        quantity: item.quantity,
        unit_price: item.menu.price,
        subtotal: item.subtotal,
        notes: item.notes || null,
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) {
        // Rollback order if items creation fails
        await supabase.from('orders').delete().eq('id', order.id);
        
        return {
          data: null,
          error: itemsError.message,
          success: false,
        };
      }

      // Get complete order with items
      const result = await this.getOrderById(order.id);
      return result;
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Update order status
   */
  static async updateOrderStatus(
    orderId: string,
    status: string
  ): Promise<ApiResponse<Order>> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({ status })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Update order payment status
   */
  static async updatePaymentStatus(
    orderId: string,
    paymentStatus: string
  ): Promise<ApiResponse<Order>> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({ payment_status: paymentStatus })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Cancel order
   */
  static async cancelOrder(orderId: string): Promise<ApiResponse<Order>> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({ 
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get today's orders
   */
  static async getTodayOrders(): Promise<ApiResponse<OrderWithItems[]>> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return this.getOrders({
      date_from: today.toISOString(),
      date_to: tomorrow.toISOString(),
    });
  }

  /**
   * Get orders by status
   */
  static async getOrdersByStatus(status: string): Promise<ApiResponse<OrderWithItems[]>> {
    return this.getOrders({ status });
  }

  /**
   * Get kitchen queue (pending and preparing orders)
   */
  static async getKitchenQueue(): Promise<ApiResponse<OrderWithItems[]>> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (
            *,
            menu:menus (*)
          ),
          cashier:profiles!orders_cashier_id_fkey (*)
        `)
        .in('status', ['pending', 'preparing'])
        .order('created_at', { ascending: true });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }

  /**
   * Get dashboard statistics
   */
  static async getDashboardStats(): Promise<ApiResponse<{
    todayOrders: number;
    todayRevenue: number;
    pendingOrders: number;
    completedOrders: number;
  }>> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Get today's orders
      const { data: todayOrders, error: todayError } = await supabase
        .from('orders')
        .select('total_amount, status')
        .gte('created_at', today.toISOString())
        .lt('created_at', tomorrow.toISOString());

      if (todayError) {
        return {
          data: null,
          error: todayError.message,
          success: false,
        };
      }

      // Calculate statistics
      const stats = {
        todayOrders: todayOrders?.length || 0,
        todayRevenue: todayOrders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0,
        pendingOrders: todayOrders?.filter(order => order.status === 'pending').length || 0,
        completedOrders: todayOrders?.filter(order => order.status === 'completed').length || 0,
      };

      return {
        data: stats,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false,
      };
    }
  }
}
