'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { AuthService } from '@/services/authService';
import { queryKeys, invalidateQueries } from '@/lib/queryClient';
import { Profile, LoginForm } from '@/types';
import { User } from '@supabase/supabase-js';

interface UseAuthReturn {
  // State
  user: User | null;
  profile: Profile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  signIn: (credentials: LoginForm) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
  
  // Mutations
  signInMutation: any;
  signOutMutation: any;
  updateProfileMutation: any;
  
  // Utilities
  hasRole: (roles: string[]) => boolean;
  isRole: (role: string) => boolean;
}

export function useAuth(): UseAuthReturn {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isInitialized, setIsInitialized] = useState(false);

  // Query for current user with profile
  const {
    data: userWithProfile,
    isLoading: isUserLoading,
    error: userError,
  } = useQuery({
    queryKey: queryKeys.auth.user,
    queryFn: AuthService.getCurrentUserWithProfile,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: false,
  });

  // Sign in mutation
  const signInMutation = useMutation({
    mutationFn: AuthService.signIn,
    onSuccess: (response) => {
      if (response.success && response.data) {
        // Update the cache with new user data
        queryClient.setQueryData(queryKeys.auth.user, response.data);
        
        // Invalidate related queries
        invalidateQueries.auth();
        
        // Redirect based on user role
        const role = response.data.profile.role;
        switch (role) {
          case 'admin':
            router.push('/dashboard/admin');
            break;
          case 'cashier':
            router.push('/dashboard/cashier');
            break;
          case 'kitchen':
            router.push('/dashboard/kitchen');
            break;
          case 'manager':
            router.push('/dashboard/admin');
            break;
          default:
            router.push('/dashboard');
        }
      }
    },
    onError: (error) => {
      console.error('Sign in error:', error);
    },
  });

  // Sign out mutation
  const signOutMutation = useMutation({
    mutationFn: AuthService.signOut,
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();
      
      // Redirect to login
      router.push('/auth/login');
    },
    onError: (error) => {
      console.error('Sign out error:', error);
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: AuthService.updateProfile,
    onSuccess: (response) => {
      if (response.success && response.data) {
        // Update the cache with new profile data
        queryClient.setQueryData(queryKeys.auth.user, (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            profile: response.data,
          };
        });
        
        // Invalidate auth queries
        invalidateQueries.auth();
      }
    },
    onError: (error) => {
      console.error('Update profile error:', error);
    },
  });

  // Listen to auth state changes
  useEffect(() => {
    const { data: { subscription } } = AuthService.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session);
        
        if (event === 'SIGNED_IN') {
          // Refetch user data
          queryClient.invalidateQueries({ queryKey: queryKeys.auth.user });
        } else if (event === 'SIGNED_OUT') {
          // Clear user data
          queryClient.setQueryData(queryKeys.auth.user, null);
          queryClient.clear();
        }
        
        setIsInitialized(true);
      }
    );

    return () => subscription.unsubscribe();
  }, [queryClient]);

  // Initialize auth state on mount
  useEffect(() => {
    if (!isInitialized) {
      AuthService.getCurrentSession().then(() => {
        setIsInitialized(true);
      });
    }
  }, [isInitialized]);

  // Helper functions
  const signIn = async (credentials: LoginForm) => {
    await signInMutation.mutateAsync(credentials);
  };

  const signOut = async () => {
    await signOutMutation.mutateAsync();
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    await updateProfileMutation.mutateAsync(updates);
  };

  const hasRole = (roles: string[]): boolean => {
    if (!userWithProfile?.profile) return false;
    return roles.includes(userWithProfile.profile.role);
  };

  const isRole = (role: string): boolean => {
    if (!userWithProfile?.profile) return false;
    return userWithProfile.profile.role === role;
  };

  return {
    // State
    user: userWithProfile?.user || null,
    profile: userWithProfile?.profile || null,
    isLoading: isUserLoading || !isInitialized,
    isAuthenticated: !!userWithProfile?.user,
    
    // Actions
    signIn,
    signOut,
    updateProfile,
    
    // Mutations
    signInMutation,
    signOutMutation,
    updateProfileMutation,
    
    // Utilities
    hasRole,
    isRole,
  };
}

// Hook for protecting routes
export function useRequireAuth(requiredRoles?: string[]) {
  const auth = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!auth.isLoading) {
      if (!auth.isAuthenticated) {
        router.push('/auth/login');
        return;
      }

      if (requiredRoles && !auth.hasRole(requiredRoles)) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [auth.isLoading, auth.isAuthenticated, auth.profile, requiredRoles, router]);

  return auth;
}

// Hook for redirecting authenticated users
export function useRedirectIfAuthenticated(redirectTo: string = '/dashboard') {
  const auth = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!auth.isLoading && auth.isAuthenticated) {
      router.push(redirectTo);
    }
  }, [auth.isLoading, auth.isAuthenticated, redirectTo, router]);

  return auth;
}
