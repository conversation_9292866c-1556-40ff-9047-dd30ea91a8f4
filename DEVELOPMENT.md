# 🛠️ Development Guide - LuPizza

Panduan lengkap untuk developer yang ingin berkontribusi atau mengembangkan sistem LuPizza.

## 📋 Prerequisites

### Software Requirements
- **Node.js** 18.0.0 atau lebih baru
- **npm** 9.0.0 atau lebih baru (atau yarn/pnpm)
- **Git** untuk version control
- **VS Code** (recommended) dengan extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter
  - ESLint

### Accounts & Services
- **Supabase Account** - untuk database dan authentication
- **Vercel Account** (optional) - untuk deployment

## 🚀 Setup Development Environment

### 1. Clone & Install
```bash
# Clone repository
git clone <repository-url>
cd lupizza-app

# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local
```

### 2. Supabase Setup
1. Buat project baru di [Supabase Dashboard](https://supabase.com/dashboard)
2. Copy Project URL dan Anon Key ke `.env.local`
3. Jalankan SQL schema dari `supabase-schema.sql`
4. Buat user demo dengan password yang sesuai

### 3. Environment Variables
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# App
NEXT_PUBLIC_APP_NAME=LuPizza
NEXT_PUBLIC_APP_VERSION=1.0.0
NODE_ENV=development
```

### 4. Run Development Server
```bash
npm run dev
```

## 🏗️ Architecture Overview

### Frontend Architecture
```
src/
├── app/                 # Next.js App Router
├── components/          # React Components
│   ├── shared/         # Reusable UI components
│   ├── cashier/        # Cashier-specific components
│   └── providers/      # Context providers
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries & configs
├── services/           # API service layers
├── types/              # TypeScript definitions
└── styles/             # Additional styles
```

### Data Flow
1. **UI Components** → trigger actions
2. **Custom Hooks** → manage state & side effects
3. **Service Layer** → handle API calls
4. **Supabase Client** → database operations
5. **React Query** → caching & synchronization

### State Management
- **React Query** untuk server state
- **React useState/useReducer** untuk local state
- **Supabase Realtime** untuk real-time updates
- **Context API** untuk global app state

## 🔧 Development Workflow

### Branch Strategy
```
main                 # Production branch
├── develop         # Development branch
├── feature/xxx     # Feature branches
├── bugfix/xxx      # Bug fix branches
└── hotfix/xxx      # Hotfix branches
```

### Commit Convention
```
feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks
```

### Code Style
- **ESLint** untuk linting
- **Prettier** untuk formatting
- **TypeScript** untuk type safety
- **Tailwind CSS** untuk styling

## 📁 File Naming Conventions

### Components
```
PascalCase untuk component files
├── Button.tsx
├── UserProfile.tsx
└── OrderSummary.tsx
```

### Hooks
```
camelCase dengan prefix 'use'
├── useAuth.ts
├── useOrders.ts
└── useLocalStorage.ts
```

### Services
```
camelCase dengan suffix 'Service'
├── authService.ts
├── orderService.ts
└── menuService.ts
```

### Types
```
PascalCase untuk interfaces/types
├── User
├── Order
└── MenuItem
```

## 🧪 Testing Strategy

### Unit Tests
```bash
# Run unit tests
npm run test

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration
```

### E2E Tests
```bash
# Run E2E tests
npm run test:e2e
```

### Testing Guidelines
- Test business logic dalam services
- Test custom hooks dengan React Testing Library
- Test critical user flows dengan E2E
- Mock external dependencies (Supabase)

## 🔍 Debugging

### Development Tools
- **React Query Devtools** - untuk monitoring queries
- **Supabase Dashboard** - untuk database debugging
- **Browser DevTools** - untuk general debugging
- **VS Code Debugger** - untuk server-side debugging

### Common Issues
1. **Supabase Connection Issues**
   - Check environment variables
   - Verify project URL dan keys
   - Check RLS policies

2. **Authentication Problems**
   - Clear browser storage
   - Check user roles dalam database
   - Verify auth policies

3. **Real-time Not Working**
   - Check Supabase realtime settings
   - Verify subscription setup
   - Check network connectivity

## 📊 Performance Guidelines

### React Query Optimization
```typescript
// Good: Specific query keys
const queryKey = ['orders', 'today', userId];

// Bad: Generic query keys
const queryKey = ['orders'];
```

### Component Optimization
```typescript
// Use React.memo untuk expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  // Component logic
});

// Use useMemo untuk expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);
```

### Database Optimization
- Gunakan indexes untuk queries yang sering digunakan
- Limit hasil query dengan pagination
- Gunakan select untuk field yang diperlukan saja
- Optimize RLS policies

## 🔐 Security Best Practices

### Authentication
- Selalu validate user session
- Implement proper role-based access
- Use secure password policies
- Implement session timeout

### Database Security
- Enable Row Level Security (RLS)
- Validate input pada service layer
- Use parameterized queries
- Implement proper error handling

### Frontend Security
- Sanitize user input
- Validate data dengan Zod schemas
- Don't expose sensitive data dalam client
- Use HTTPS dalam production

## 🚀 Deployment

### Build Process
```bash
# Build untuk production
npm run build

# Test production build locally
npm run start
```

### Environment Setup
```bash
# Production environment variables
NEXT_PUBLIC_SUPABASE_URL=production-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=production-key
NODE_ENV=production
```

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificate configured
- [ ] Error monitoring setup
- [ ] Performance monitoring setup
- [ ] Backup strategy implemented

## 📈 Monitoring & Analytics

### Error Tracking
- Setup error boundary components
- Implement error logging
- Monitor Supabase errors
- Track user actions

### Performance Monitoring
- Monitor Core Web Vitals
- Track API response times
- Monitor database performance
- Track user engagement

## 🤝 Contributing Guidelines

### Pull Request Process
1. Fork repository
2. Create feature branch
3. Implement changes dengan tests
4. Update documentation
5. Submit pull request
6. Code review process
7. Merge setelah approval

### Code Review Checklist
- [ ] Code follows style guidelines
- [ ] Tests are included dan passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance impact considered
- [ ] Accessibility guidelines followed

## 📚 Learning Resources

### Next.js
- [Next.js Documentation](https://nextjs.org/docs)
- [Next.js Learn Course](https://nextjs.org/learn)

### Supabase
- [Supabase Documentation](https://supabase.com/docs)
- [Supabase University](https://supabase.com/docs/guides/getting-started)

### React Query
- [TanStack Query Documentation](https://tanstack.com/query/latest)
- [React Query Best Practices](https://tkdodo.eu/blog/practical-react-query)

### TypeScript
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [TypeScript with React](https://react-typescript-cheatsheet.netlify.app/)

---

**Happy Coding! 🚀**
