'use client';

import { useRequireAuth } from '@/hooks/useAuth';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/shared/Card';
import { Badge } from '@/components/shared/Badge';
import { 
  ShoppingCart, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  TrendingUp,
  Users,
  Pizza,
  ChefHat
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';

// Mock data - akan diganti dengan data real dari API
const mockStats = {
  todayOrders: 45,
  todayRevenue: 2750000,
  pendingOrders: 8,
  completedOrders: 37,
  activeStaff: 6,
  popularMenu: 'Pizza Margherita',
};

const mockRecentOrders = [
  {
    id: '1',
    order_number: 'LP240116001',
    customer_name: '<PERSON> Rizki',
    total_amount: 125000,
    status: 'preparing',
    created_at: new Date().toISOString(),
  },
  {
    id: '2',
    order_number: 'LP240116002',
    customer_name: '<PERSON><PERSON>',
    total_amount: 89000,
    status: 'ready',
    created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
  },
  {
    id: '3',
    order_number: 'LP240116003',
    customer_name: 'Budi Santoso',
    total_amount: 156000,
    status: 'completed',
    created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
  },
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'pending':
      return <Badge variant="warning">Menunggu</Badge>;
    case 'preparing':
      return <Badge variant="default">Diproses</Badge>;
    case 'ready':
      return <Badge variant="success">Siap</Badge>;
    case 'completed':
      return <Badge variant="secondary">Selesai</Badge>;
    case 'cancelled':
      return <Badge variant="destructive">Dibatalkan</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

export default function DashboardPage() {
  const { profile } = useRequireAuth();

  if (!profile) return null;

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div>
          <h1 className="text-3xl font-bold">
            Selamat datang, {profile.full_name || profile.email}!
          </h1>
          <p className="text-muted-foreground mt-2">
            Berikut adalah ringkasan aktivitas hari ini di LuPizza.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pesanan Hari Ini
              </CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.todayOrders}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+12%</span> dari kemarin
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pendapatan Hari Ini
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(mockStats.todayRevenue)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+8%</span> dari kemarin
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pesanan Menunggu
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.pendingOrders}</div>
              <p className="text-xs text-muted-foreground">
                Perlu segera diproses
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pesanan Selesai
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.completedOrders}</div>
              <p className="text-xs text-muted-foreground">
                Dari {mockStats.todayOrders} total pesanan
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Orders and Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Orders */}
          <Card>
            <CardHeader>
              <CardTitle>Pesanan Terbaru</CardTitle>
              <CardDescription>
                Pesanan yang baru masuk dan sedang diproses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRecentOrders.map((order) => (
                  <div
                    key={order.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="space-y-1">
                      <p className="font-medium">{order.order_number}</p>
                      <p className="text-sm text-muted-foreground">
                        {order.customer_name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDateTime(order.created_at)}
                      </p>
                    </div>
                    <div className="text-right space-y-1">
                      <p className="font-medium">
                        {formatCurrency(order.total_amount)}
                      </p>
                      {getStatusBadge(order.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Aksi Cepat</CardTitle>
              <CardDescription>
                Akses fitur yang sering digunakan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {profile.role === 'cashier' || profile.role === 'admin' ? (
                  <a
                    href="/dashboard/cashier"
                    className="flex flex-col items-center p-4 border rounded-lg hover:bg-accent transition-colors"
                  >
                    <ShoppingCart className="h-8 w-8 text-primary mb-2" />
                    <span className="text-sm font-medium">Buat Pesanan</span>
                  </a>
                ) : null}

                {profile.role === 'kitchen' || profile.role === 'admin' ? (
                  <a
                    href="/dashboard/kitchen"
                    className="flex flex-col items-center p-4 border rounded-lg hover:bg-accent transition-colors"
                  >
                    <ChefHat className="h-8 w-8 text-primary mb-2" />
                    <span className="text-sm font-medium">Dapur</span>
                  </a>
                ) : null}

                {profile.role === 'admin' || profile.role === 'manager' ? (
                  <a
                    href="/dashboard/menu"
                    className="flex flex-col items-center p-4 border rounded-lg hover:bg-accent transition-colors"
                  >
                    <Pizza className="h-8 w-8 text-primary mb-2" />
                    <span className="text-sm font-medium">Kelola Menu</span>
                  </a>
                ) : null}

                {profile.role === 'admin' || profile.role === 'manager' ? (
                  <a
                    href="/dashboard/staff"
                    className="flex flex-col items-center p-4 border rounded-lg hover:bg-accent transition-colors"
                  >
                    <Users className="h-8 w-8 text-primary mb-2" />
                    <span className="text-sm font-medium">Karyawan</span>
                  </a>
                ) : null}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Info for Admin/Manager */}
        {(profile.role === 'admin' || profile.role === 'manager') && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Karyawan Aktif
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockStats.activeStaff}</div>
                <p className="text-xs text-muted-foreground">
                  Sedang bertugas hari ini
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Menu Terpopuler
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{mockStats.popularMenu}</div>
                <p className="text-xs text-muted-foreground">
                  15 pesanan hari ini
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Rata-rata Pesanan
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(Math.round(mockStats.todayRevenue / mockStats.todayOrders))}
                </div>
                <p className="text-xs text-muted-foreground">
                  Per pesanan hari ini
                </p>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
