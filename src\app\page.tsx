"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { LoadingPage } from "@/components/shared/Loading";

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, isLoading, profile } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated && profile) {
        // Redirect based on user role
        switch (profile.role) {
          case "admin":
            router.push("/dashboard/admin");
            break;
          case "cashier":
            router.push("/dashboard/cashier");
            break;
          case "kitchen":
            router.push("/dashboard/kitchen");
            break;
          case "manager":
            router.push("/dashboard/admin");
            break;
          default:
            router.push("/dashboard");
        }
      } else {
        router.push("/auth/login");
      }
    }
  }, [isAuthenticated, isLoading, profile, router]);

  return <LoadingPage text="Mengalihkan..." />;
}
